{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["__FIREFOX__"], "daemon": false, "tasks": {"ready": {"dependsOn": ["^ready"], "outputs": ["dist/**", "build/**"]}, "dev": {"dependsOn": ["ready"], "outputs": ["dist/**", "build/**", "i18n/locales/**"], "cache": false, "persistent": true}, "build": {"dependsOn": ["^build"], "outputs": ["../../dist/**", "dist/**", "build/**"], "cache": false}, "e2e": {"cache": false}, "type-check": {"cache": false}, "lint": {"cache": false}, "lint:fix": {"cache": false}, "prettier": {"cache": false}, "clean:node_modules": {"dependsOn": ["^clean:node_modules"], "cache": false}, "clean:turbo": {"dependsOn": ["^clean:turbo"], "cache": false}, "clean:bundle": {"dependsOn": ["^clean:bundle"], "cache": false}, "clean": {"dependsOn": ["^clean"], "cache": false}}}