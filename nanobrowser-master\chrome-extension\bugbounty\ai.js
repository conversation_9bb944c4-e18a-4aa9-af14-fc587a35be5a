// وحدة استدعاء الذكاء الاصطناعي مع تحميل البرومبت من ملف خارجي
export async function analyzeWithAI(jsonData) {
  // تحميل البرومبت من ملف ثابت
  const prompt = await fetch(chrome.runtime.getURL('prompt_template.txt')).then(r => r.text());
  const finalPrompt = prompt.replace('{json_data}', JSON.stringify(jsonData, null, 2));
  // استدعاء API الذكاء الاصطناعي
  const response = await fetch("http://localhost:1234/v1/chat/completions", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      model: "deepseek-coder-6.7b-instruct",
      messages: [
        { role: "system", content: "أنت مساعد أمن سيبراني خبير، قم بتحليل الأكواد واكتشاف الثغرات." },
        { role: "user", content: finalPrompt }
      ]
    })
  });
  const data = await response.json();
  return data.choices?.[0]?.message?.content || 'لا يوجد رد من الذكاء الاصطناعي.';
}
