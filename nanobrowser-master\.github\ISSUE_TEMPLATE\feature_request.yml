name: Feature
description: Suggest an idea for Nanobrowser
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature!
        
  - type: textarea
    id: problem
    attributes:
      label: What problem does this solve?
      placeholder: I'm frustrated when [...]
    validations:
      required: true
      
  - type: textarea
    id: solution
    attributes:
      label: Proposed solution
      description: What would you like to see happen?
    validations:
      required: true
      
  - type: textarea
    id: context
    attributes:
      label: Additional context
      description: Add any relevant context, screenshots, or mockups