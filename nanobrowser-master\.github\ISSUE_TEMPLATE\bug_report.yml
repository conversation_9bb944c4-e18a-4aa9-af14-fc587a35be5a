name: Bug Report
description: Create a report to help us improve NanoBrowser
title: "[Bug]: "
labels: ["bug", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

  - type: input
    id: version
    attributes:
      label: Nanobrowser Version
      description: What version of NanoBrowser are you running?
      placeholder: e.g., 0.1.0
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Tell us what happened...
    validations:
      required: true
      
  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. My prompt is '...'
        2. I click '...'
        3. I see '...'
    validations:
      required: true
      
  - type: dropdown
    id: llm_provider
    attributes:
      label: LLM Service Provider
      description: Which LLM service provider are you using?
      options:
        - OpenAI
        - Anthropic
        - Google Gemini
        - Ollama
        - OpenRouter
        - Other (please specify in description)
    validations:
      required: true
      
  - type: textarea
    id: models
    attributes:
      label: Models Used
      description: Which models are you using for the 3 agents (Plan<PERSON>, Navigator, <PERSON><PERSON><PERSON>)?
      placeholder: |
        Planner: gpt-4o
        Navigator: claude-3.7-sonnet
        Validator: gemini-2.0-flash
    validations:
      required: true
 
  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem