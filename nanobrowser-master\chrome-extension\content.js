// content.js: تحميل BugBountyCore وتشغيل الفحص من صفحة الويب
import BugBountyCore from './bugbounty/BugBountyCore.js';

window.bugBountyCore = new BugBountyCore();

chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.action === 'startBugBountyScan') {
    window.bugBountyCore.startComprehensiveScan(window.location.href)
      .then(results => {
        chrome.runtime.sendMessage({ action: 'bugBountyScanResult', results });
      });
    sendResponse({ status: 'started' });
  }
});
