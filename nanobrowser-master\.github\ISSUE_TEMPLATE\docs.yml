name: Documentation
description: Suggest a change to our documentation
title: "[Docs]: "
labels: ["docs", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        If you are unsure if the docs are relevant or needed, please open up a discussion first.
  - type: textarea
    attributes:
      label: Describe the change
      description: |
        Please describe the documentation you want to change or add, and if it is for end-users or contributors.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Additional context
      description: Add any other context to the feature (like screenshots, resources)