{"name": "@extension/hmr", "version": "0.1.7", "description": "chrome extension - hot module reload/refresh", "private": true, "sideEffects": true, "files": ["dist/**"], "main": "dist/index.js", "module": "dist/index.js", "types": "index.ts", "scripts": {"clean:bundle": "rimraf dist && pnpx rimraf build", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "build:tsc": "tsc -b tsconfig.build.json", "build:rollup": "rollup --config rollup.config.mjs", "ready": "pnpm run build:tsc && pnpm run build:rollup", "dev": "node dist/lib/initializers/initReloadServer.js", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "@rollup/plugin-sucrase": "^5.0.2", "@types/ws": "^8.5.13", "esm": "^3.2.25", "fast-glob": "^3.3.2", "rollup": "^4.24.0", "ts-node": "^10.9.2", "ws": "8.18.0"}}