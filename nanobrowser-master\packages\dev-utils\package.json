{"name": "@extension/dev-utils", "version": "0.1.7", "description": "chrome extension - dev utils", "private": true, "sideEffects": false, "files": ["dist/**"], "main": "dist/index.js", "module": "dist/index.js", "types": "index.ts", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "tsc", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "@extension/shared": "workspace:*"}}