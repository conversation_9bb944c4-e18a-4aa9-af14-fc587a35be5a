// popup.js: واجهة المستخدم لزر الفحص ونتائج الذكاء الاصطناعي

const scanBtn = document.getElementById('scanBtn');
const statusDiv = document.getElementById('status');
const resultsDiv = document.getElementById('results');

function saveResults(results) {
  localStorage.setItem('bugbounty_scan_results', JSON.stringify(results));
}

function loadResults() {
  const data = localStorage.getItem('bugbounty_scan_results');
  return data ? JSON.parse(data) : null;
}

function renderResults(results) {
  if (!results) {
    resultsDiv.innerHTML = '<em>لا توجد نتائج محفوظة.</em>';
    return;
  }
  let html = '';
  if (results.vulnerabilities && results.vulnerabilities.length) {
    html += '<h4>الثغرات المكتشفة:</h4>';
    results.vulnerabilities.forEach(vuln => {
      html += `<div class="vuln">• ${vuln}</div>`;
    });
  }
  if (results.aiResponse) {
    html += `<div class="ai-response"><b>رد الذكاء الاصطناعي:</b><br>${results.aiResponse}</div>`;
  }
  resultsDiv.innerHTML = html;
}

scanBtn.onclick = async () => {
  scanBtn.disabled = true;
  statusDiv.textContent = 'جاري الفحص...';
  resultsDiv.innerHTML = '';
  try {
    // إرسال رسالة إلى background لبدء الفحص
    chrome.runtime.sendMessage({ action: 'startBugBountyScan' }, (response) => {
      if (response && response.status === 'started') {
        statusDiv.textContent = 'تم بدء الفحص، انتظر النتائج...';
      } else {
        statusDiv.textContent = 'تعذر بدء الفحص.';
        scanBtn.disabled = false;
      }
    });
  } catch (e) {
    statusDiv.textContent = 'حدث خطأ.';
    scanBtn.disabled = false;
  }
};

// استقبال النتائج من background
chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.action === 'bugBountyScanResult') {
    statusDiv.textContent = 'انتهى الفحص.';
    renderResults(msg.results);
    saveResults(msg.results);
    scanBtn.disabled = false;
  }
});

// تحميل النتائج السابقة عند فتح popup
renderResults(loadResults());
